PIL/BdfFontFile.py,sha256=KcXWcefHzxlJIo6etOUyjQ4JVFZgmZnN4QDzxZfNkFw,3359
PIL/BlpImagePlugin.py,sha256=tE5vVzces4fAq5gdWwp0jY2AmhOMFGJJeZeNkAX79DQ,16008
PIL/BmpImagePlugin.py,sha256=KbmyIRUgi2ST2UosM2xoxUCbfbxwykgNC2NxMK7S3xc,18143
PIL/BufrStubImagePlugin.py,sha256=yOLBoyNiE52G1dOve9kX_mE_cm9ynuxZ33e0r9twL48,1630
PIL/ContainerIO.py,sha256=NmRN9naqGy3KuR9nup6o1ihJRStbBHk-vyNpJb6oQF8,3003
PIL/CurImagePlugin.py,sha256=4DOiIo2AhLI95foB8E0DWhnomyEwWwEtSAP5wgWxyHM,1796
PIL/DcxImagePlugin.py,sha256=RwMLnvYc_CsQVgRYABQr7afzRyuGL0RfyH5PFo0dMC0,2037
PIL/DdsImagePlugin.py,sha256=oa3hFToLXR4hp3EoqwWnPH9BpPksVklBzvpOFoB1Cck,9885
PIL/EpsImagePlugin.py,sha256=OMTQUQ7tWDuzrORLSYxKorD4qeAH_mTS8ZSz8SNZa0Y,16396
PIL/ExifTags.py,sha256=6HINw53r9dPBMb9d7B43zZ-gNtrMRqoRk3LthY_FDpo,10098
PIL/FitsImagePlugin.py,sha256=RaHhfQLWQmlDbW1XM7ClczkOlSMFZbFyLQIPMwsUJVI,2136
PIL/FliImagePlugin.py,sha256=w4nu2ueoBDH4BSetCqGYj19r_bbRpb0YbWwaocwkDBI,4615
PIL/FontFile.py,sha256=btTE3c7rQJXwM9Ndz3_IV9Q-h2sM2Bj3t_4UuoQld34,2874
PIL/FpxImagePlugin.py,sha256=Go_wldBehPM9_1pzRJ2dXK_Rp9DlArA4EbSXnD0pToI,7215
PIL/FtexImagePlugin.py,sha256=Jh2AmDqS1Ilah9LoakzZHXHB4o6UmCb_ZBJyiPWlQqM,3543
PIL/GbrImagePlugin.py,sha256=NYF1dk30el-Q2mIhh-oDIO_fd7SsR_FVzwJWnLuXSjI,3012
PIL/GdImageFile.py,sha256=1f6XhnLB5iLQNWUqyl0DwjeZ0JFmfNdkUb8KeWPlFFs,2705
PIL/GifImagePlugin.py,sha256=ePLDlY2GGDtk85w9WA6E63plLAGC-mz3fEsHMzayb-4,36721
PIL/GimpGradientFile.py,sha256=gmeJv2hu0EegYmkoN7ULuf0HPtj6PPqV1F-sPUsruco,3533
PIL/GimpPaletteFile.py,sha256=IfG8kYjL76X3sK7Sxr9DFz7FrSaSrebWYNid8bdBIaQ,1401
PIL/GribStubImagePlugin.py,sha256=c32uOGd20JahVVKQ_MSHUIIZKvzJ_zwoWBPxTNQc7cQ,1624
PIL/Hdf5StubImagePlugin.py,sha256=u4S4774og7xJJghN2pMaZr0nC8WAG85ApKc5LHNmY1o,1627
PIL/IcnsImagePlugin.py,sha256=k4lbT0hUOK2NXJRdVvQzILzR7y2k4-knpnaCEd72Z_U,12327
PIL/IcoImagePlugin.py,sha256=TskEB51DQjGcUNbdrgRbJyljnOsUirLLgqSnFMxnlK0,11981
PIL/ImImagePlugin.py,sha256=cc1UJFXvmOcVMwkzN3BUQWNQMI9OAxOLyTDPH_f3NV8,11241
PIL/Image.py,sha256=gmGeN2h0bBrM26x3Nap2t2XZWqgZzHmkwM9Z0UKlI8Y,138191
PIL/ImageChops.py,sha256=xfHglbl9ENPlsehI9tBeiod1lVUy_5AKN949WL2uRlM,7306
PIL/ImageCms.py,sha256=0OSnMPKtits0fRhpZ7-YZV04r5W05mCOF2Q7JJ_3R90,38180
PIL/ImageColor.py,sha256=no5pGI-pZ7BoWhkqxzK8ZTkCyZ14NgzCntlj_OYjwpE,9397
PIL/ImageDraw.py,sha256=KlVY-NHP7VjVu_AXgea8JK1xxdukc-U0t-RhrnoAI8k,37406
PIL/ImageDraw2.py,sha256=zgaZG4cEzid2wnV5EH-rORnulhzpxBtuebGW2818q7c,5694
PIL/ImageEnhance.py,sha256=tHMwYy_knQ_tice_c5MzDShwkFVL5DyJ1EVm1KHiDDI,3293
PIL/ImageFile.py,sha256=tieTgFwl9L15kiKyOE7syh0jjOARZe2wp1MAi-XJJdU,24312
PIL/ImageFilter.py,sha256=8LucVba1feXfkmu194TDiRzsloLY1SMOmjrjgvvc8JM,17707
PIL/ImageFont.py,sha256=_yWWxP8RZbcconpag7xvA3ycugPdijJlU1DZ3AcqgiQ,61228
PIL/ImageGrab.py,sha256=s8w6MFX1I9EHom-J7Y44I3zetfdRtMDwtr2TabPk-MA,5772
PIL/ImageMath.py,sha256=RKwRujskwGxCX3StfabbHJgH1d4IUCkL1RwmtMIFPMA,7620
PIL/ImageMode.py,sha256=VAdsoGIHDNkQeg5FU92bf3ROHIA9KK2J7HQpgLWyyls,3004
PIL/ImageMorph.py,sha256=JrTMGN0WyGjur-We0qmGl0Pb_S_F60s4ADPGvrmOH2c,8231
PIL/ImageOps.py,sha256=9olrV5OLu0YDcjhpL95IvotklYWP2F_1II548s8MTSQ,23135
PIL/ImagePalette.py,sha256=k6uizgl-YEmkgsxWSYsV1gnMK1n5IXK63i0gbkCLaH4,8174
PIL/ImagePath.py,sha256=IZ7vxarm_tI2DuV7QPchZe1P4U52ypHbO-v3JkcGm44,355
PIL/ImageQt.py,sha256=vof7oBSSN8dVxP9_J_rLOxRKrRV6w5r4_U0UFjhQgXw,6582
PIL/ImageSequence.py,sha256=4Rn6Jx4fc2oNu3vKUCjMhCEBwBoaRfExmrJGpjrJmHI,1948
PIL/ImageShow.py,sha256=90X78lQKT7TpzKWTig6yUMQE-rljMD2myDvspXyzEd8,8631
PIL/ImageStat.py,sha256=Tr4x_f_wE6wM2l6RtSmPxWXjkbtD63HSrHAKeR1j9Ps,4072
PIL/ImageTk.py,sha256=qqYH6DV1yf5ucJT8AFaTlMy6QBSYxUQsZV6LWGRmUrg,8744
PIL/ImageTransform.py,sha256=EsgO8FV2Gnm1hBMVx-8i7I3bhehRfMlwHIsV7QQ7FjM,2985
PIL/ImageWin.py,sha256=qklIa-nlezkM_BUVRaIOgjSqRDWjQq8Qxe_r3sQy3Ro,7421
PIL/ImtImagePlugin.py,sha256=qLOLEtX0p68oIpOL3XukwMamXGkGJu0f7lLmqD90xUg,2681
PIL/IptcImagePlugin.py,sha256=eWk_Nfm_iPadBen58L_cLRlMiMJh9-thLjkaTj8HuLk,6042
PIL/Jpeg2KImagePlugin.py,sha256=cxYjnllrWu_dYog-4iUPmOxpNrHq30HH1-J7l5FyZGw,11984
PIL/JpegImagePlugin.py,sha256=GREozVTeSJSPQkYXoIRrlMv3yL1ijtEMcq5ERtX3Jfo,30208
PIL/JpegPresets.py,sha256=7lEumxxIdQrdc4Eync4R8IItvu7WyP6KKY9vaR3RHfY,12583
PIL/McIdasImagePlugin.py,sha256=T-tdJMczBROlQcBvkCee8yVEJrenSArb18N6lYJCnoo,1872
PIL/MicImagePlugin.py,sha256=S2sxzkk7NAWrVfBujixUksnm8tWFLOQ5cZfgRwLXhkM,2617
PIL/MpegImagePlugin.py,sha256=MqlkgKmmeqKTBhhkwOYXnUGtxxs0HxnD0NHgcGGgIaM,1906
PIL/MpoImagePlugin.py,sha256=uhDiuxP_j7teKZwTFkDh7eF93iofseoVhB8hDvQiCjE,6486
PIL/MspImagePlugin.py,sha256=Mv7VJp2QPU9NyY4KDI5nh7Ec-6EJYXQD6AgVzSAhXhQ,5807
PIL/PSDraw.py,sha256=rxUa015jdjOMgQFsIi8DPaHxCNaBN-l0FIWMB03Let0,6754
PIL/PaletteFile.py,sha256=JuiaGSo0Kt7P_R2alJ4iALW93cTM_J7KLAdqgZwWyrQ,1179
PIL/PalmImagePlugin.py,sha256=vVD0dymsz8RLhavbcua_x6z4VvpWyG_UqBfeZqno3EM,9369
PIL/PcdImagePlugin.py,sha256=1RnWPybnbvv9zoeDoZe9zCp8b4pAZNv19hOKnMLHLas,1559
PIL/PcfFontFile.py,sha256=sRkDm6QE_7iByrCJABM8BRu8D3xrxX-nudj0tZfFT6M,7013
PIL/PcxImagePlugin.py,sha256=zJQ56HkBMR238BG650JZoICxgMeq8JnYqIZ5KgKcDEM,6243
PIL/PdfImagePlugin.py,sha256=m3tBBNYMFKXVTRG4ZrYIxhZFTRFBTwgPh6qCnkn4rR8,9090
PIL/PdfParser.py,sha256=vdzuWpLrco9QSf4ng1HnNuLHEIV1K4PwI9BN8YBYXXY,35397
PIL/PixarImagePlugin.py,sha256=vkyv-L1tMHM31lgHd-Igc2MWD4GMvGwr4dRXYI1sVog,1721
PIL/PngImagePlugin.py,sha256=k_NpZ1tPD97l5yUDFrA8y1jPIflOTzbN9zBmlTkrPOw,47677
PIL/PpmImagePlugin.py,sha256=Lve90Z-88hAr5S_-B9u9Zot6_qbCXgkit0Bwb8ANw6w,11749
PIL/PsdImagePlugin.py,sha256=Lk-8cuQDkEXsby1vqRXsGh9OvLrr-6Pkv_pYBNJf7Ew,7840
PIL/PyAccess.py,sha256=xZNnmpbjFOT_aoJrbveoePZYJ-MnuPQHjeOQIO4nSjI,10261
PIL/QoiImagePlugin.py,sha256=4IRz2OCmigQ143rZVL_6hEkmqeKInezU3k5sCoFZUXc,3740
PIL/SgiImagePlugin.py,sha256=lAqSLC-WBE0QFlbh35BuAbMnPXvJjeAJ2T-2uAD2FpY,6410
PIL/SpiderImagePlugin.py,sha256=YCeytCXDdp9AYU4tnGeS-iGmrwZo0I9xh0ZhsEzdiTQ,9789
PIL/SunImagePlugin.py,sha256=yAFDntos9MwxCjqhUMtO81wSdZtVvG55LmU3IPuQeIA,4545
PIL/TarIO.py,sha256=7VMckCocQZ-tbUngzJcFovYSH8nq-p_dKt0NLYAdsR8,1557
PIL/TgaImagePlugin.py,sha256=1hOdD0d6ZcZpo0g-nhftbp1wS62T3I6Jnnx-07bmf3g,6836
PIL/TiffImagePlugin.py,sha256=Z5gXwTzA62oVeXIfgDZ5x-I4qklZGgriDV9uFJ-JuAU,78848
PIL/TiffTags.py,sha256=7hsZaJPN097IFRzSHTCYjUa8GRcm3ty-lIcby80leHM,17374
PIL/WalImageFile.py,sha256=3H0iIuWq8uyfA8f_fdknRV_HeymPvF2seg2Z_NCHuLE,5643
PIL/WebPImagePlugin.py,sha256=zkcW4SOMlDft-DCqCZmcisTmArGH8n4u3vS8F5E5oIY,11601
PIL/WmfImagePlugin.py,sha256=Jh2_6YJL_RCoyinb-TPGyTOgw-Pu4qEgPXfL9W8hSxs,4869
PIL/XVThumbImagePlugin.py,sha256=y2pL6xka8TpSr1H4tPDSjwysoXsYOOmFexcQexI0hkI,2065
PIL/XbmImagePlugin.py,sha256=6F48itYy9KJElsuQTv0L3btRhs5VRkli3ttfK2W221c,2582
PIL/XpmImagePlugin.py,sha256=FLQDG7QMeOY2GzJxf-xvQfnt0q22Kxda5cUX-m2O00A,3313
PIL/__init__.py,sha256=aVBgHCsv9BcnTp7EGqnduEhZYdkF8HwkevMPk6yp03Q,2063
PIL/__main__.py,sha256=hOw0dx7KqDFGy9lxphlkL6NmaCbj8lp294vXH4n35ko,44
PIL/__pycache__/BdfFontFile.cpython-38.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-38.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-38.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-38.pyc,,
PIL/__pycache__/ContainerIO.cpython-38.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-38.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-38.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/ExifTags.cpython-38.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-38.pyc,,
PIL/__pycache__/FontFile.cpython-38.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-38.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-38.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-38.pyc,,
PIL/__pycache__/GdImageFile.cpython-38.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-38.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-38.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-38.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-38.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-38.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-38.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-38.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-38.pyc,,
PIL/__pycache__/Image.cpython-38.pyc,,
PIL/__pycache__/ImageChops.cpython-38.pyc,,
PIL/__pycache__/ImageCms.cpython-38.pyc,,
PIL/__pycache__/ImageColor.cpython-38.pyc,,
PIL/__pycache__/ImageDraw.cpython-38.pyc,,
PIL/__pycache__/ImageDraw2.cpython-38.pyc,,
PIL/__pycache__/ImageEnhance.cpython-38.pyc,,
PIL/__pycache__/ImageFile.cpython-38.pyc,,
PIL/__pycache__/ImageFilter.cpython-38.pyc,,
PIL/__pycache__/ImageFont.cpython-38.pyc,,
PIL/__pycache__/ImageGrab.cpython-38.pyc,,
PIL/__pycache__/ImageMath.cpython-38.pyc,,
PIL/__pycache__/ImageMode.cpython-38.pyc,,
PIL/__pycache__/ImageMorph.cpython-38.pyc,,
PIL/__pycache__/ImageOps.cpython-38.pyc,,
PIL/__pycache__/ImagePalette.cpython-38.pyc,,
PIL/__pycache__/ImagePath.cpython-38.pyc,,
PIL/__pycache__/ImageQt.cpython-38.pyc,,
PIL/__pycache__/ImageSequence.cpython-38.pyc,,
PIL/__pycache__/ImageShow.cpython-38.pyc,,
PIL/__pycache__/ImageStat.cpython-38.pyc,,
PIL/__pycache__/ImageTk.cpython-38.pyc,,
PIL/__pycache__/ImageTransform.cpython-38.pyc,,
PIL/__pycache__/ImageWin.cpython-38.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-38.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-38.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-38.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-38.pyc,,
PIL/__pycache__/JpegPresets.cpython-38.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-38.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PSDraw.cpython-38.pyc,,
PIL/__pycache__/PaletteFile.cpython-38.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PcfFontFile.cpython-38.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PdfParser.cpython-38.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-38.pyc,,
PIL/__pycache__/PyAccess.cpython-38.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-38.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-38.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-38.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-38.pyc,,
PIL/__pycache__/TarIO.cpython-38.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-38.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-38.pyc,,
PIL/__pycache__/TiffTags.cpython-38.pyc,,
PIL/__pycache__/WalImageFile.cpython-38.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-38.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-38.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-38.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-38.pyc,,
PIL/__pycache__/__init__.cpython-38.pyc,,
PIL/__pycache__/__main__.cpython-38.pyc,,
PIL/__pycache__/_binary.cpython-38.pyc,,
PIL/__pycache__/_deprecate.cpython-38.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-38.pyc,,
PIL/__pycache__/_util.cpython-38.pyc,,
PIL/__pycache__/_version.cpython-38.pyc,,
PIL/__pycache__/features.cpython-38.pyc,,
PIL/_binary.py,sha256=Ts2HKoKEMc9N4DsgIYTmJM_ecjKsexxJhsL6zR0tmuQ,2145
PIL/_deprecate.py,sha256=vEwjD8W0zIMC9haa5owrlO4XlyKnlSxGmycoIS7EPGg,2005
PIL/_imaging.cp38-win_amd64.pyd,sha256=iLKfziUsc-7zx1kg9ro_yxQZdAO6inAHKxcXe5gvDPE,2581504
PIL/_imagingcms.cp38-win_amd64.pyd,sha256=RJZq3ISg0fAy4FRio3nIdkIKmld3774K_-SBhzJfPGQ,256512
PIL/_imagingft.cp38-win_amd64.pyd,sha256=bvURoQemqUnncmIoEHBG0yRozVfBYmD4Cj03d_ajXkg,1796608
PIL/_imagingmath.cp38-win_amd64.pyd,sha256=lLOUqvhnjfCkfSBoNMntxakQuZAuHDVPGYodY-BqFj4,24064
PIL/_imagingmorph.cp38-win_amd64.pyd,sha256=3oPy1nJJVCVJb1w7cJqSLgx0P332q9iNE4PknRq7P7A,13312
PIL/_imagingtk.cp38-win_amd64.pyd,sha256=yrndQYEk5L2ymPd-lQSxi1nY3kvj4nXUGpZknVx1yps,14848
PIL/_tkinter_finder.py,sha256=ovavtmTb5qIMcBIypI9W8t2Hl-fnOv0ZHoJrhSUtHEI,520
PIL/_util.py,sha256=sX8hjjr5oCOQNLChyFM7lP5ZaKIpISa4Sc0vuclsR-4,388
PIL/_version.py,sha256=3f5N_I2TZ0Hjo8p7uch0q-RgOODqnxUg0VFvAVN8mSw,53
PIL/_webp.cp38-win_amd64.pyd,sha256=RVhVyD7I8Sm1L4idKkWIv5EzYS2skGBzcLr-UqlJYSA,533504
PIL/features.py,sha256=7NWnyiIGGk2O6OJvrrd_s7tjgHAt3Pw9r9mUwxIvU7Y,9947
Pillow-10.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
Pillow-10.1.0.dist-info/LICENSE,sha256=k3ctiSRH8veNu9qxjx3Lo5WFAiOCEH-eYI5bbu6yeho,56518
Pillow-10.1.0.dist-info/METADATA,sha256=5sEjK8LzcIJdqvD-6Yr9JMmVWcN7coGPcys2aOuvO3c,9635
Pillow-10.1.0.dist-info/RECORD,,
Pillow-10.1.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
Pillow-10.1.0.dist-info/WHEEL,sha256=KplWMgwSZbeAOumvxNxIrVbNPnn_LVzfBH7l38jDCVM,100
Pillow-10.1.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
Pillow-10.1.0.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2

importlib_resources-6.4.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
importlib_resources-6.4.5.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
importlib_resources-6.4.5.dist-info/METADATA,sha256=iwGfOzcc9jUBSuQa6zQeXla0el4YCZlAoj5uRIm7fyg,3975
importlib_resources-6.4.5.dist-info/RECORD,,
importlib_resources-6.4.5.dist-info/WHEEL,sha256=cVxcB9AmuTcXqmwrtPhNK88dr7IR_b6qagTj0UvIEbY,91
importlib_resources-6.4.5.dist-info/top_level.txt,sha256=fHIjHU1GZwAjvcydpmUnUrTnbvdiWjG4OEVZK8by0TQ,20
importlib_resources/__init__.py,sha256=3J-261Qqzg-1kBenMVsOsvJo19EbUmYqjHkrZWiFHOM,703
importlib_resources/__pycache__/__init__.cpython-38.pyc,,
importlib_resources/__pycache__/_adapters.cpython-38.pyc,,
importlib_resources/__pycache__/_common.cpython-38.pyc,,
importlib_resources/__pycache__/_functional.cpython-38.pyc,,
importlib_resources/__pycache__/_itertools.cpython-38.pyc,,
importlib_resources/__pycache__/abc.cpython-38.pyc,,
importlib_resources/__pycache__/readers.cpython-38.pyc,,
importlib_resources/__pycache__/simple.cpython-38.pyc,,
importlib_resources/_adapters.py,sha256=vprJGbUeHbajX6XCuMP6J3lMrqCi-P_MTlziJUR7jfk,4482
importlib_resources/_common.py,sha256=5PVT4ezn_Ptj7LIAebtLYquK7A6X4EYoQJM37yTBdbQ,5624
importlib_resources/_functional.py,sha256=mLU4DwSlh8_2IXWqwKOfPVxyRqAEpB3B4XTfRxr3X3M,2651
importlib_resources/_itertools.py,sha256=eDisV6RqiNZOogLSXf6LOGHOYc79FGgPrKNLzFLmCrU,1277
importlib_resources/abc.py,sha256=UKNU9ncEDkZRB3txcGb3WLxsL2iju9JbaLTI-dfLE_4,5162
importlib_resources/compat/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
importlib_resources/compat/__pycache__/__init__.cpython-38.pyc,,
importlib_resources/compat/__pycache__/py38.cpython-38.pyc,,
importlib_resources/compat/__pycache__/py39.cpython-38.pyc,,
importlib_resources/compat/py38.py,sha256=MWhut3XsAJwBYUaa5Qb2AoCrZNqcQjVThP-P1uBoE_4,230
importlib_resources/compat/py39.py,sha256=KlP7QiD3NkoytWFjJgrJI33uRTw667vEDtkZWUtBDZM,152
importlib_resources/future/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
importlib_resources/future/__pycache__/__init__.cpython-38.pyc,,
importlib_resources/future/__pycache__/adapters.cpython-38.pyc,,
importlib_resources/future/adapters.py,sha256=1-MF2VRcCButhcC1OMfZILU9o3kwZ4nXB2lurXpaIAw,2940
importlib_resources/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
importlib_resources/readers.py,sha256=n9Rn8B5UHapkXGSfFhQNbdk_pfDCISPBLIXZnpoOKs8,6251
importlib_resources/simple.py,sha256=wJm2qGZ9EMPFhRLiJBa9Em5tVKbD7Q8ibWtt4ZNgWBU,2590
importlib_resources/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
importlib_resources/tests/__pycache__/__init__.cpython-38.pyc,,
importlib_resources/tests/__pycache__/_path.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_compatibilty_files.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_contents.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_custom.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_files.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_functional.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_open.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_path.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_read.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_reader.cpython-38.pyc,,
importlib_resources/tests/__pycache__/test_resource.cpython-38.pyc,,
importlib_resources/tests/__pycache__/util.cpython-38.pyc,,
importlib_resources/tests/__pycache__/zip.cpython-38.pyc,,
importlib_resources/tests/_path.py,sha256=eqMTKkYA9Hc8_mzrVBN14HPuoc2HFNxuyTWqAo_2ZEM,2288
importlib_resources/tests/compat/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
importlib_resources/tests/compat/__pycache__/__init__.cpython-38.pyc,,
importlib_resources/tests/compat/__pycache__/py312.cpython-38.pyc,,
importlib_resources/tests/compat/__pycache__/py39.cpython-38.pyc,,
importlib_resources/tests/compat/py312.py,sha256=qcWjpZhQo2oEsdwIlRRQHrsMGDltkFTnETeG7fLdUS8,364
importlib_resources/tests/compat/py39.py,sha256=nOjut4CZDtRgZEblX9hWhupo9vli_CA1270_JwdQzRo,441
importlib_resources/tests/test_compatibilty_files.py,sha256=95N_R7aik8cvnE6sBJpsxmP0K5plOWRIJDgbalD-Hpw,3314
importlib_resources/tests/test_contents.py,sha256=EagRx9Mz7MOe1kRaOc9XNX_YlBYy90Qzhv2rqWMhMlw,837
importlib_resources/tests/test_custom.py,sha256=QrHZqIWl0e-fsQRfm0ych8stOlKJOsAIU3rK6QOcyN0,1221
importlib_resources/tests/test_files.py,sha256=4G4Wo4_2UJtpg9HrEKjRaXimM68lXKjkAmFY-9OLCyE,5796
importlib_resources/tests/test_functional.py,sha256=DV8sdnwtpacNjHQ3ExifIoeQTCr4C-M-NVbaWewOcAo,8863
importlib_resources/tests/test_open.py,sha256=eCDLP6SszzBK6vs5j6LDkuR0Y2rHXrTYNRVZsAFKnCQ,2681
importlib_resources/tests/test_path.py,sha256=rz_BOqNzEu5ZRyrhAHOi9tvx3K_0AqHiRNkYT4QFhQw,1985
importlib_resources/tests/test_read.py,sha256=oh0ZkCZ04N0DxPXgBLdadENwia4J0_sYt73uaH_I11g,3045
importlib_resources/tests/test_reader.py,sha256=G62D2vkFjYSeFT6BFE-z_shUX8n7h80f089ojXCufBY,4655
importlib_resources/tests/test_resource.py,sha256=ZxW6cIXJRwgvU7-wCc2jIRWV9M4pX4uznijN0vNfplM,7686
importlib_resources/tests/util.py,sha256=CtY1uL-Xl71sw553ow2st6M5X710jf2jrhYAv6nhZ_Y,6065
importlib_resources/tests/zip.py,sha256=nG9D6u_4nu67NrEUFfMt7vzba0qO_-QPyAAWc6W4gP4,577

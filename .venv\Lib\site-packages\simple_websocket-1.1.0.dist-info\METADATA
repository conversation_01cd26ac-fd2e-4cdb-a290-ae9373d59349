Metadata-Version: 2.1
Name: simple-websocket
Version: 1.1.0
Summary: Simple WebSocket server and client for Python
Author-email: <PERSON> <<EMAIL>>
Project-URL: Homepage, https://github.com/miguelgrinberg/simple-websocket
Project-URL: Bug Tracker, https://github.com/miguelgrinberg/simple-websocket/issues
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: wsproto
Provides-Extra: dev
Requires-Dist: tox ; extra == 'dev'
Requires-Dist: flake8 ; extra == 'dev'
Requires-Dist: pytest ; extra == 'dev'
Requires-Dist: pytest-cov ; extra == 'dev'
Provides-Extra: docs
Requires-Dist: sphinx ; extra == 'docs'

simple-websocket
================

[![Build status](https://github.com/miguelgrinberg/simple-websocket/workflows/build/badge.svg)](https://github.com/miguelgrinberg/simple-websocket/actions) [![codecov](https://codecov.io/gh/miguelgrinberg/simple-websocket/branch/main/graph/badge.svg)](https://codecov.io/gh/miguelgrinberg/simple-websocket)

Simple WebSocket server and client for Python.

## Resources

- [Documentation](http://simple-websocket.readthedocs.io/en/latest/)
- [PyPI](https://pypi.python.org/pypi/simple-websocket)
- [Change Log](https://github.com/miguelgrinberg/simple-websocket/blob/main/CHANGES.md)


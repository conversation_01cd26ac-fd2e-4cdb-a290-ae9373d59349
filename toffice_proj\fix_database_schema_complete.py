#!/usr/bin/env python3
"""
Complete database schema fix for Taluk Office Digital File Management System
This script will update the database schema to match the current models
"""

import sqlite3
import os
from datetime import datetime

def backup_database(db_path):
    """Create a backup of the database before making changes"""
    if os.path.exists(db_path):
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    return None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def add_missing_columns(cursor, table_name, columns_to_add):
    """Add missing columns to a table"""
    for column_name, column_definition in columns_to_add.items():
        if not check_column_exists(cursor, table_name, column_name):
            try:
                cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}")
                print(f"✅ Added column {column_name} to {table_name}")
            except sqlite3.Error as e:
                print(f"⚠️ Error adding column {column_name} to {table_name}: {e}")

def create_missing_tables(cursor):
    """Create any missing tables"""
    
    # Create bulk_data_access_sessions table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS bulk_data_access_sessions (
            id INTEGER PRIMARY KEY,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            user_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            unlock_criteria TEXT,
            criteria_count INTEGER DEFAULT 0,
            ip_address VARCHAR(45),
            user_agent TEXT,
            access_count INTEGER DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """)
    print("✅ Created/verified bulk_data_access_sessions table")
    
    # Create bulk_data_access_logs table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS bulk_data_access_logs (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            session_id INTEGER,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            action VARCHAR(50) NOT NULL,
            search_criteria TEXT,
            criteria_count INTEGER DEFAULT 0,
            files_accessed INTEGER DEFAULT 0,
            access_granted BOOLEAN DEFAULT 0,
            denial_reason VARCHAR(255),
            ip_address VARCHAR(45),
            user_agent TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (session_id) REFERENCES bulk_data_access_sessions (id)
        )
    """)
    print("✅ Created/verified bulk_data_access_logs table")
    
    # Create compartment_qrs table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS compartment_qrs (
            id INTEGER PRIMARY KEY,
            compartment_number INTEGER UNIQUE NOT NULL,
            bundle_range_start INTEGER NOT NULL,
            bundle_range_end INTEGER NOT NULL,
            qr_image_path VARCHAR(255) NOT NULL,
            qr_data TEXT NOT NULL,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    """)
    print("✅ Created/verified compartment_qrs table")
    
    # Create bundles table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS bundles (
            id INTEGER PRIMARY KEY,
            bundle_number INTEGER UNIQUE NOT NULL,
            compartment_id INTEGER,
            qr_code_path VARCHAR(255),
            qr_data TEXT,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (compartment_id) REFERENCES compartment_qrs (id)
        )
    """)
    print("✅ Created/verified bundles table")
    
    # Create bundle_assignments table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS bundle_assignments (
            id INTEGER PRIMARY KEY,
            bundle_id INTEGER NOT NULL,
            file_id INTEGER NOT NULL,
            assigned_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            assigned_by INTEGER,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (bundle_id) REFERENCES bundles (id),
            FOREIGN KEY (file_id) REFERENCES files (id),
            FOREIGN KEY (assigned_by) REFERENCES users (id)
        )
    """)
    print("✅ Created/verified bundle_assignments table")
    
    # Create bundle_statistics table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS bundle_statistics (
            id INTEGER PRIMARY KEY,
            bundle_id INTEGER NOT NULL,
            total_files INTEGER DEFAULT 0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (bundle_id) REFERENCES bundles (id)
        )
    """)
    print("✅ Created/verified bundle_statistics table")

def update_files_table(cursor):
    """Update the files table with missing columns"""
    files_columns = {
        'excel_row_data': 'TEXT',
        'import_warnings': 'TEXT',
        'data_quality_score': 'INTEGER DEFAULT 100',
        'import_batch_id': 'VARCHAR(50)',
        'original_row_number': 'INTEGER'
    }
    add_missing_columns(cursor, 'files', files_columns)

def update_access_logs_table(cursor):
    """Update the access_logs table with security columns"""
    access_logs_columns = {
        'is_bulk_data': 'BOOLEAN DEFAULT 0',
        'access_granted': 'BOOLEAN DEFAULT 1',
        'denial_reason': 'VARCHAR(255)',
        'access_session_id': 'INTEGER',
        'ip_address': 'VARCHAR(45)',
        'user_agent': 'TEXT'
    }
    add_missing_columns(cursor, 'access_logs', access_logs_columns)

def fix_database_schema(db_path):
    """Main function to fix database schema"""
    print(f"🔧 Fixing database schema for: {db_path}")
    
    # Backup database
    backup_path = backup_database(db_path)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n📋 Creating missing tables...")
        create_missing_tables(cursor)
        
        print("\n📋 Updating files table...")
        update_files_table(cursor)
        
        print("\n📋 Updating access_logs table...")
        update_access_logs_table(cursor)
        
        # Commit changes
        conn.commit()
        print("\n✅ Database schema updated successfully!")
        
        # Verify the changes
        print("\n🔍 Verifying schema changes...")
        cursor.execute("PRAGMA table_info(files)")
        files_columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = ['excel_row_data', 'import_warnings', 'data_quality_score', 'import_batch_id', 'original_row_number']
        missing_columns = [col for col in required_columns if col not in files_columns]
        
        if missing_columns:
            print(f"⚠️ Still missing columns in files table: {missing_columns}")
        else:
            print("✅ All required columns present in files table")
        
        # List all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"\n📊 Database tables: {', '.join(tables)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing database schema: {e}")
        if backup_path and os.path.exists(backup_path):
            print(f"💾 Backup available at: {backup_path}")
        return False

def main():
    """Main execution function"""
    print("🏛️ TALUK OFFICE DATABASE SCHEMA FIX")
    print("=" * 50)
    
    # Database paths to check and fix
    db_paths = [
        'toffice.db',
        'instance/taluk_office.db'
    ]
    
    success_count = 0
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"\n🔧 Processing database: {db_path}")
            if fix_database_schema(db_path):
                success_count += 1
        else:
            print(f"ℹ️ Database not found: {db_path}")
    
    print("\n" + "=" * 50)
    print("📊 SCHEMA FIX SUMMARY")
    print("=" * 50)
    print(f"✅ Databases processed successfully: {success_count}")
    
    if success_count > 0:
        print("\n🎉 Database schema fix completed!")
        print("🚀 You can now restart the application.")
    else:
        print("\n⚠️ No databases were processed successfully.")

if __name__ == "__main__":
    main()

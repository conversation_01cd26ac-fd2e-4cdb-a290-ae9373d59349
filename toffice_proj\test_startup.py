#!/usr/bin/env python3
"""
Test script to diagnose startup issues and run the Flask application
"""

import sys
import os
import traceback

print("=== Taluk Office System Startup Test ===")
print(f"Python version: {sys.version}")
print(f"Current working directory: {os.getcwd()}")

# Test all imports
try:
    print("\n=== Testing Imports ===")
    
    print("Importing Flask...")
    from flask import Flask
    print("✅ Flask imported")
    
    print("Importing extensions...")
    from extensions import db, login_manager, socketio
    print("✅ Extensions imported")
    
    print("Importing models...")
    from models.user import User
    from models.file import File
    from models.location import Location
    from models.access_log import AccessLog
    from models.bulk_data_security import BulkDataAccessSession, DataEncryption
    from models.bundle import Bundle
    print("✅ Models imported")
    
    print("Importing config...")
    from config import Config
    print("✅ Config imported")
    
    print("Importing utilities...")
    from utils.security import bulk_data_access_required
    from utils.bundle_manager import BundleManager
    print("✅ Utilities imported")
    
    print("\n=== Creating Flask App ===")
    app = Flask(__name__)
    app.config.from_object(Config)
    print("✅ Flask app created")
    
    print("Initializing extensions...")
    db.init_app(app)
    login_manager.init_app(app)
    socketio.init_app(app)
    print("✅ Extensions initialized")
    
    print("\n=== Testing Database ===")
    with app.app_context():
        print("Creating database tables...")
        db.create_all()
        print("✅ Database tables created")
        
        # Check if default users exist
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Creating default users...")
            # Create admin user
            admin_user = User(username='admin', email='<EMAIL>', role='Administrator')
            admin_user.set_password('admin123')
            admin_user.is_admin = True
            db.session.add(admin_user)
            
            # Create officer user
            officer_user = User(username='officer', email='<EMAIL>', role='Officer')
            officer_user.set_password('officer123')
            db.session.add(officer_user)
            
            # Create clerk user
            clerk_user = User(username='clerk', email='<EMAIL>', role='Clerk')
            clerk_user.set_password('clerk123')
            db.session.add(clerk_user)
            
            db.session.commit()
            print("✅ Default users created")
        else:
            print("✅ Default users already exist")
    
    print("\n=== Starting Flask Application ===")
    print("🚀 Starting server on http://127.0.0.1:5001")
    print("Press Ctrl+C to stop the server")
    
    # Start the application
    app.run(debug=True, port=5001, host='127.0.0.1')
    
except Exception as e:
    print(f"\n❌ Error occurred: {e}")
    print("\n=== Full Traceback ===")
    traceback.print_exc()
    sys.exit(1)

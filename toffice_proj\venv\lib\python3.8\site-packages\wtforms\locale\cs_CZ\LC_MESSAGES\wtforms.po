# Czech (Czechia) translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 2.0.2dev\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-10-05 13:42+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: cz <<EMAIL>>\n"
"Language: cs_CZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=((n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2)\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Neplatný název pole '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Hodnota pole má být stejná jako u %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Počet znaků daného pole má být minimálně %(min)d."
msgstr[1] "Počet znaků daného pole má být minimálně %(min)d."
msgstr[2] ""

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Počet znaků daného pole má byt maximálně %(max)d."
msgstr[1] "Počet znaků daného pole má byt maximálně %(max)d."
msgstr[2] ""

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Délka pole ma být mezi %(min)d a %(max)d."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Hodnota čísla má být alespoň %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Hodnota čísla má být maximálně %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Hodnota čísla má být mezi %(min)s and %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Toto pole je povinné."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Neplatný vstup."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Neplatná emailová adresa."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Neplatná IP adresa."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Neplatná MAC adresa."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "Neplatné URL."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "Neplatné UUID."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Neplatná hodnota, povolené hodnoty jsou: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Neplatná hodnota, nesmí být mezi: %(values)s."

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field is required."
msgid "This field cannot be edited"
msgstr "Toto pole je povinné."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value"
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Neplatný CSRF token."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Chybí CSRF token."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "Chyba CSRF."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Hodnota CSRF tokenu."

#: src/wtforms/fields/choices.py:135
msgid "Invalid Choice: could not coerce."
msgstr "Neplatná volba: nelze převést."

#: src/wtforms/fields/choices.py:139 src/wtforms/fields/choices.py:192
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/choices.py:148
msgid "Not a valid choice."
msgstr "Neplatná volba."

#: src/wtforms/fields/choices.py:185
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr "Neplatná volba: jeden nebo více datových vstupů nemohou být převedeny."

#: src/wtforms/fields/choices.py:204
#, fuzzy, python-format
#| msgid "'%(value)s' is not a valid choice for this field."
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' není platnou volbou pro dané pole."
msgstr[1] "'%(value)s' není platnou volbou pro dané pole."
msgstr[2] "'%(value)s' není platnou volbou pro dané pole."

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Neplatná hodnota pro datum a čas."

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Neplatná hodnota pro datum."

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr ""

#: src/wtforms/fields/datetime.py:148
#, fuzzy
#| msgid "Not a valid date value."
msgid "Not a valid week value."
msgstr "Neplatná hodnota pro datum."

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Neplatná hodnota pro celé číslo."

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Neplatná hodnota pro desetinné číslo."

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Neplatná hodnota pro desetinné číslo."

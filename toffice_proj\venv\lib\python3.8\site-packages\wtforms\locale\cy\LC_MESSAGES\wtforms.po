# Welsh translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 2.0dev\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-10-05 13:42+0200\n"
"PO-Revision-Date: 2015-01-29 14:07+0000\n"
"Last-Translator: <PERSON> <EMAIL>\n"
"Language-Team: cy <<EMAIL>>\n"
"Language: cy\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Enw maes annilys '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Rhaid i'r maes fod yr un fath â/ag %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Mae'n rhaid i Maes fod o leiaf %(min)d cymeriad hir."
msgstr[1] "Mae'n rhaid i Maes fod o leiaf %(min)d nod o hyd."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Ni all Maes fod yn hirach na %(max)d cymeriad."
msgstr[1] "Ni all Maes fod yn fwy na %(max)d cymeriadau."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Rhaid i'r maes fod rhwng %(min)d a %(max)d o nodau"

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Rhaid i'r rhif fod o leiaf %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Ni chaiff y rhif fod yn fwy na %(max)s. "

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Rhaid i'r rhif fod rhwng %(min)s a %(max)s. "

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Rhaid cwblhau'r maes hwn."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Mewnbwn annilys"

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Cyfeiriad e-bost annilys"

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Cyfeiriad IP annilys"

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Cyfeiriad Mac annilys."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "URL annilys."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "UUID annilys."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Gwerth annilys, rhaid i'r gwerth fod yn un o'r canlynol: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Gwerth annilys, ni all fod yn un o'r canlynol: %(values)s"

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field is required."
msgid "This field cannot be edited"
msgstr "Rhaid cwblhau'r maes hwn."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value"
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Tocyn CSRF annilys"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Tocyn CSRF ar goll"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF wedi methu"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Tocyn CSRF wedi dod i ben"

#: src/wtforms/fields/choices.py:135
msgid "Invalid Choice: could not coerce."
msgstr "Dewis annilys: ddim yn bosib gweithredu"

#: src/wtforms/fields/choices.py:139 src/wtforms/fields/choices.py:192
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/choices.py:148
msgid "Not a valid choice."
msgstr "Nid yw hwn yn ddewis dilys"

#: src/wtforms/fields/choices.py:185
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Dewis(iadau) annilys: ddim yn bosib gweithredu un neu ragor o fewnbynnau data"

#: src/wtforms/fields/choices.py:204
#, fuzzy, python-format
#| msgid "'%(value)s' is not a valid choice for this field."
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "Nid yw '%(value)s' yn ddewis dilys ar gyfer y maes hwn"
msgstr[1] "Nid yw '%(value)s' yn ddewis dilys ar gyfer y maes hwn"

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Gwerth dyddiad/amser annilys"

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Gwerth dyddiad annilys"

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr ""

#: src/wtforms/fields/datetime.py:148
#, fuzzy
#| msgid "Not a valid date value."
msgid "Not a valid week value."
msgstr "Gwerth dyddiad annilys"

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Gwerth integer annilys"

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Gwerth degolyn annilys"

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Gwerth float annilys"

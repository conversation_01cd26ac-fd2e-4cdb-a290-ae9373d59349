#!/usr/bin/env python3
"""
Verification script for enhanced compartment assignment integration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_integration():
    """Verify that compartment assignment integration is properly configured"""
    
    print("🔍 Verifying Enhanced Compartment Assignment Integration...")
    print("=" * 70)
    
    try:
        from app import app
        from utils.bundle_manager import BundleManager
        from models.bundle import Bundle
        from models.compartment_qr import CompartmentQR
        
        with app.app_context():
            print("✅ Application imports successful")
            
            # Test 1: Verify BundleManager enhanced method
            print("\n📋 Testing BundleManager Enhanced Methods:")
            print("-" * 50)
            
            # Test the enhanced assign_file_to_compartment_by_bundle method
            try:
                warnings = []
                result, error = BundleManager.assign_file_to_compartment_by_bundle(
                    file_id=999,  # Dummy file ID for testing
                    bundle_number=150,
                    warnings_list=warnings
                )
                
                if result:
                    print("✅ Enhanced assignment method works")
                    print(f"   - Bundle: {result.get('bundle_number')}")
                    print(f"   - Compartment: {result.get('compartment_number')}")
                    print(f"   - Expected Compartment: {result.get('expected_compartment')}")
                else:
                    print(f"⚠️ Assignment method returned error: {error}")
                    
            except Exception as e:
                print(f"❌ Error testing assignment method: {str(e)}")
            
            # Test 2: Verify compartment range logic
            print("\n🎯 Testing Compartment Range Logic:")
            print("-" * 50)
            
            test_bundles = [1, 200, 400, 401, 600, 800, 0, 801]
            for bundle_num in test_bundles:
                try:
                    compartment = BundleManager.get_compartment_for_bundle(bundle_num)
                    if compartment:
                        print(f"✅ Bundle {bundle_num:3d} → Compartment {compartment.compartment_number}")
                    else:
                        print(f"❌ Bundle {bundle_num:3d} → No compartment (expected for invalid bundles)")
                except Exception as e:
                    print(f"❌ Bundle {bundle_num:3d} → Error: {str(e)}")
            
            # Test 3: Verify route configuration
            print("\n🔗 Testing Route Configuration:")
            print("-" * 50)
            
            required_routes = [
                'admin_bulk_upload',
                'admin_bulk_upload_process',
                'compartment_qr_management',
                'view_compartment_qr',
                'view_compartment_bundle_files'
            ]
            
            routes = {rule.endpoint: rule.rule for rule in app.url_map.iter_rules()}
            
            for route_name in required_routes:
                if route_name in routes:
                    print(f"✅ {route_name}: {routes[route_name]}")
                else:
                    print(f"❌ Missing route: {route_name}")
            
            # Test 4: Verify template files
            print("\n📁 Testing Template Files:")
            print("-" * 50)
            
            template_files = [
                'templates/admin_bulk_upload.html',
                'templates/view_compartment_qr.html',
                'templates/compartment_bundle_files.html'
            ]
            
            for template_file in template_files:
                if os.path.exists(template_file):
                    print(f"✅ {template_file}")
                else:
                    print(f"❌ Missing: {template_file}")
            
            # Test 5: Verify database models
            print("\n🗄️ Testing Database Models:")
            print("-" * 50)
            
            try:
                # Test Bundle model
                bundle_count = Bundle.query.count()
                print(f"✅ Bundle model accessible - {bundle_count} bundles in database")
                
                # Test CompartmentQR model
                compartment_count = CompartmentQR.query.count()
                print(f"✅ CompartmentQR model accessible - {compartment_count} compartments in database")
                
                # Test compartment ranges
                for comp_num in [1, 2]:
                    comp = CompartmentQR.query.filter_by(compartment_number=comp_num).first()
                    if comp:
                        print(f"✅ Compartment {comp_num}: Bundles {comp.bundle_range_start}-{comp.bundle_range_end}")
                    else:
                        print(f"⚠️ Compartment {comp_num}: Not found in database")
                        
            except Exception as e:
                print(f"❌ Database model error: {str(e)}")
            
            # Test 6: Verify enhanced functionality
            print("\n⚡ Testing Enhanced Functionality:")
            print("-" * 50)
            
            # Check if enhanced assignment logic is present
            try:
                import inspect
                method_source = inspect.getsource(BundleManager.assign_file_to_compartment_by_bundle)
                
                enhancements = [
                    ('Enhanced validation', 'Enhanced validation with detailed feedback' in method_source),
                    ('Compartment feedback', 'expected_compartment' in method_source),
                    ('Detailed error messages', 'too low' in method_source and 'too high' in method_source),
                    ('QR generation', 'generate_bundle_qr_code' in method_source),
                    ('Comprehensive info', 'assignment_status' in method_source)
                ]
                
                for feature, present in enhancements:
                    status = "✅" if present else "❌"
                    print(f"{status} {feature}: {'Present' if present else 'Missing'}")
                    
            except Exception as e:
                print(f"❌ Error checking enhancements: {str(e)}")
            
            print("\n" + "=" * 70)
            print("🎉 Integration verification completed!")
            
            return True
            
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Verification error: {str(e)}")
        return False

def print_integration_summary():
    """Print summary of integration features"""
    print("\n📋 INTEGRATION FEATURES SUMMARY")
    print("=" * 70)
    
    print("\n🔄 Enhanced Bulk Upload Process:")
    print("  • Automatic compartment assignment based on bundle numbers")
    print("  • Enhanced validation with detailed error messages")
    print("  • Comprehensive feedback in upload summary")
    print("  • Quick access buttons to compartment views")
    print("  • 100% data preservation with graceful error handling")
    
    print("\n🎯 Compartment QR Integration:")
    print("  • Clickable bundle numbers in compartment views")
    print("  • Bundle-specific file listings with drill-down navigation")
    print("  • Real-time search within bundle views")
    print("  • Comprehensive file information display")
    print("  • Security logging for all access")
    
    print("\n📊 Assignment Logic:")
    print("  • Bundles 1-400 → Compartment 1")
    print("  • Bundles 401-800 → Compartment 2")
    print("  • Invalid bundles handled gracefully")
    print("  • Auto-creation of bundle records")
    print("  • QR code generation for new bundles")
    
    print("\n🛡️ Security Features:")
    print("  • Role-based access control maintained")
    print("  • Bulk data access logging")
    print("  • Audit trail for all compartment assignments")
    print("  • Data validation and error handling")
    print("  • Session-based access controls")
    
    print("\n🎨 User Experience:")
    print("  • Intuitive drill-down navigation")
    print("  • Visual feedback for all interactions")
    print("  • Responsive design for all devices")
    print("  • Clear error messages and guidance")
    print("  • Quick access to related functionality")

def print_testing_checklist():
    """Print testing checklist for verification"""
    print("\n✅ TESTING CHECKLIST")
    print("=" * 70)
    
    checklist_items = [
        "Upload test Excel file with various bundle numbers",
        "Verify 100% import success rate",
        "Check compartment assignment summary display",
        "Test quick access buttons to compartments",
        "Verify clickable bundle numbers in compartment views",
        "Test bundle-specific file listings",
        "Verify drill-down navigation works correctly",
        "Test search functionality within bundles",
        "Check error handling for invalid bundle access",
        "Verify security logging captures all access",
        "Test responsive design on different devices",
        "Validate data integrity throughout process"
    ]
    
    for i, item in enumerate(checklist_items, 1):
        print(f"  {i:2d}. ☐ {item}")
    
    print(f"\nTotal items to verify: {len(checklist_items)}")

if __name__ == "__main__":
    try:
        success = verify_integration()
        print_integration_summary()
        print_testing_checklist()
        
        if success:
            print("\n🎉 Verification completed successfully!")
            print("🚀 Ready to test enhanced compartment assignment functionality!")
            print("\n📝 Next steps:")
            print("1. Run: python test_enhanced_compartment_assignment.py")
            print("2. Start application: python app.py")
            print("3. Upload test file and verify functionality")
            sys.exit(0)
        else:
            print("\n❌ Verification failed!")
            print("Please check the errors above and fix them before testing.")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error during verification: {str(e)}")
        sys.exit(1)

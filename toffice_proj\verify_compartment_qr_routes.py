#!/usr/bin/env python3
"""
Verification script to check that all compartment QR routes are properly configured
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app

def verify_routes():
    """Verify that all compartment QR routes are properly configured"""
    
    print("🔍 Verifying Compartment QR Routes Configuration...")
    print("=" * 60)
    
    with app.app_context():
        # Get all routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': rule.rule
            })
        
        # Check for required compartment QR routes
        required_routes = [
            {
                'endpoint': 'compartment_qr_management',
                'rule': '/compartment-qr',
                'description': 'Compartment QR management page'
            },
            {
                'endpoint': 'view_compartment_qr',
                'rule': '/compartment-qr/<int:compartment_number>',
                'description': 'View specific compartment QR with clickable bundles'
            },
            {
                'endpoint': 'view_compartment_bundle_files',
                'rule': '/compartment-qr/<int:compartment_number>/bundle/<int:bundle_number>',
                'description': 'View files in specific bundle (NEW ROUTE)'
            },
            {
                'endpoint': 'get_compartment_qr_image',
                'rule': '/compartment-qr/<int:compartment_number>/image',
                'description': 'Serve compartment QR code image'
            },
            {
                'endpoint': 'download_compartment_qr',
                'rule': '/compartment-qr/<int:compartment_number>/download',
                'description': 'Download compartment QR code'
            }
        ]
        
        print("📋 Checking Required Routes:")
        print("-" * 40)
        
        all_routes_found = True
        
        for required_route in required_routes:
            found = False
            for route in routes:
                if route['endpoint'] == required_route['endpoint']:
                    found = True
                    print(f"✅ {required_route['endpoint']}")
                    print(f"   Route: {required_route['rule']}")
                    print(f"   Description: {required_route['description']}")
                    print(f"   Methods: {route['methods']}")
                    print()
                    break
            
            if not found:
                print(f"❌ MISSING: {required_route['endpoint']}")
                print(f"   Expected Route: {required_route['rule']}")
                print(f"   Description: {required_route['description']}")
                print()
                all_routes_found = False
        
        # Check for related routes
        print("🔗 Related Routes:")
        print("-" * 40)
        
        related_routes = [
            'compartment_bundles',
            'bundle_detail',
            'view_file',
            'get_qrcode'
        ]
        
        for endpoint in related_routes:
            found = False
            for route in routes:
                if route['endpoint'] == endpoint:
                    found = True
                    print(f"✅ {endpoint}: {route['rule']}")
                    break
            
            if not found:
                print(f"⚠️  {endpoint}: Not found (may affect functionality)")
        
        print()
        print("📁 Template Files Check:")
        print("-" * 40)
        
        # Check for required template files
        template_files = [
            'templates/compartment_qr.html',
            'templates/view_compartment_qr.html',
            'templates/compartment_bundle_files.html',
            'templates/compartment_bundles.html'
        ]
        
        for template_file in template_files:
            if os.path.exists(template_file):
                print(f"✅ {template_file}")
            else:
                print(f"❌ MISSING: {template_file}")
                all_routes_found = False
        
        print()
        print("🧪 Test Files Check:")
        print("-" * 40)
        
        test_files = [
            'test_compartment_bundle_drill_down.py',
            'COMPARTMENT_QR_BUNDLE_DRILL_DOWN.md',
            'COMPARTMENT_QR_IMPLEMENTATION_SUMMARY.md'
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"✅ {test_file}")
            else:
                print(f"⚠️  {test_file}: Not found")
        
        print()
        print("=" * 60)
        
        if all_routes_found:
            print("🎉 SUCCESS: All compartment QR routes are properly configured!")
            print("✅ The bundle drill-down functionality should work correctly.")
            print()
            print("🚀 Next Steps:")
            print("1. Start the application: python app.py")
            print("2. Access: http://127.0.0.1:5001")
            print("3. Go to Dashboard > Compartment QR")
            print("4. Test the bundle drill-down functionality")
            print("5. Upload test data using: python test_compartment_bundle_drill_down.py")
        else:
            print("❌ ISSUES FOUND: Some routes or files are missing!")
            print("Please check the missing items above and fix them.")
            return False
        
        return True

def print_test_urls():
    """Print test URLs for manual verification"""
    print()
    print("🔗 Test URLs for Manual Verification:")
    print("-" * 40)
    print("Base URL: http://127.0.0.1:5001")
    print()
    print("1. Compartment QR Management:")
    print("   http://127.0.0.1:5001/compartment-qr")
    print()
    print("2. Compartment 1 View (with clickable bundles):")
    print("   http://127.0.0.1:5001/compartment-qr/1")
    print()
    print("3. Compartment 2 View (with clickable bundles):")
    print("   http://127.0.0.1:5001/compartment-qr/2")
    print()
    print("4. Bundle-specific file listings (after uploading test data):")
    print("   http://127.0.0.1:5001/compartment-qr/1/bundle/1")
    print("   http://127.0.0.1:5001/compartment-qr/1/bundle/2")
    print("   http://127.0.0.1:5001/compartment-qr/2/bundle/450")
    print("   http://127.0.0.1:5001/compartment-qr/2/bundle/451")
    print()
    print("5. Invalid bundle access (should show error):")
    print("   http://127.0.0.1:5001/compartment-qr/1/bundle/500")
    print("   http://127.0.0.1:5001/compartment-qr/2/bundle/50")

if __name__ == "__main__":
    try:
        success = verify_routes()
        print_test_urls()
        
        if success:
            print("\n✅ Verification completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Verification failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error during verification: {str(e)}")
        sys.exit(1)

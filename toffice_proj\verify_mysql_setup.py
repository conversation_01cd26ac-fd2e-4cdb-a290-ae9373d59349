#!/usr/bin/env python3
"""
Verify MySQL setup and show user accounts
"""

import pymysql

def verify_setup():
    """Verify the MySQL database setup"""
    try:
        # Connect to the database
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='Surendar@369',
            database='taluk_office_db',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🏛️  Taluk Office MySQL Database Verification")
        print("=" * 60)
        
        # Show database info
        cursor.execute("SELECT DATABASE()")
        db_name = cursor.fetchone()[0]
        print(f"✅ Connected to database: {db_name}")
        
        # Show tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"\n📋 Tables created: {len(tables)}")
        for table in tables:
            print(f"   - {table[0]}")
        
        # Show users
        cursor.execute("SELECT id, username, email, role, is_admin, created_at FROM users")
        users = cursor.fetchall()
        
        print(f"\n👥 User Accounts: {len(users)}")
        print("-" * 60)
        for user in users:
            user_id, username, email, role, is_admin, created_at = user
            admin_status = "Admin" if is_admin else "User"
            print(f"ID: {user_id}")
            print(f"Username: {username}")
            print(f"Email: {email}")
            print(f"Role: {role}")
            print(f"Status: {admin_status}")
            print(f"Created: {created_at}")
            print("-" * 60)
        
        # Show login credentials
        print("\n🔐 Login Credentials:")
        print("=" * 60)
        print("Administrator:")
        print("  Username: admin")
        print("  Password: admin123")
        print("  Role: Administrator")
        print()
        print("Officer:")
        print("  Username: officer")
        print("  Password: officer123")
        print("  Role: Officer")
        print()
        print("Clerk:")
        print("  Username: clerk")
        print("  Password: clerk123")
        print("  Role: Clerk")
        print()
        
        print("🌐 Application URL: http://127.0.0.1:5000")
        print("📊 Database: MySQL (taluk_office_db)")
        print("🔒 Password: Surendar@369")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    verify_setup()
